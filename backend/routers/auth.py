
"""
Authentication router
Handles user registration, login, and authentication-related endpoints
"""

from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import OAuth2Pass<PERSON><PERSON>e<PERSON>Form
from sqlalchemy.orm import Session
from models.database import get_db
from models.models import User
from schemas.auth import (
    UserCreate, UserResponse, UserLogin, Token,
    PasswordReset, PasswordResetConfirm, EmailVerification,
    ChangePassword, UserUpdate
)
from auth.security import (
    get_password_hash, authenticate_user, create_access_token,
    create_email_verification_token, verify_email_token,
    create_password_reset_token, verify_password_reset_token,
    verify_password, ACCESS_TOKEN_EXPIRE_MINUTES
)
from auth.dependencies import get_current_user, get_current_active_user


router = APIRouter(prefix="/api/v1/auth", tags=["authentication"])


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new user

    Args:
        user: User creation data
        db: Database session

    Returns:
        Created user data

    Raises:
        HTTPException: If email or username already exists
    """
    # Check if email already exists
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Check if username already exists
    db_user = db.query(User).filter(User.username == user.username).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )

    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = User(
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=False  # Will be verified via email
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # TODO: Send verification email
    # For now, we'll auto-verify in development
    # In production, this should send an email with verification token

    return db_user


@router.post("/login", response_model=Token)
async def login_user(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Login user and return access token

    Args:
        form_data: Login form data (username and password)
        db: Database session

    Returns:
        Access token and token type

    Raises:
        HTTPException: If credentials are incorrect
    """
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Type narrowing: at this point user is guaranteed to be a User object, not False
    assert isinstance(user, User), "User should be a User object after authentication"
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Return in seconds
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user information

    Args:
        current_user: Current authenticated user

    Returns:
        Current user data
    """
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user profile

    Args:
        user_update: User update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        Updated user data
    """
    # Update only provided fields
    if user_update.full_name is not None:
        current_user.full_name = user_update.full_name

    if user_update.avatar_url is not None:
        current_user.avatar_url = user_update.avatar_url

    db.commit()
    db.refresh(current_user)

    return current_user


@router.post("/change-password")
async def change_password(
    password_data: ChangePassword,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Change user password

    Args:
        password_data: Current and new password
        current_user: Current authenticated user
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If current password is incorrect
    """
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )

    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    db.commit()

    return {"message": "Password updated successfully"}


@router.post("/request-password-reset")
async def request_password_reset(
    password_reset: PasswordReset,
    db: Session = Depends(get_db)
):
    """
    Request password reset

    Args:
        password_reset: Email for password reset
        db: Database session

    Returns:
        Success message (always returns success for security)
    """
    user = db.query(User).filter(User.email == password_reset.email).first()

    if user:
        # Create password reset token
        reset_token = create_password_reset_token(user.email)

        # TODO: Send password reset email
        # For now, we'll just return the token (for development)
        # In production, this should send an email with the reset link

        print(f"Password reset token for {user.email}: {reset_token}")

    # Always return success message for security
    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """
    Reset password with token

    Args:
        reset_data: Reset token and new password
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If token is invalid or expired
    """
    email = verify_password_reset_token(reset_data.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )

    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Update password
    user.hashed_password = get_password_hash(reset_data.new_password)
    db.commit()

    return {"message": "Password reset successfully"}


@router.post("/verify-email")
async def verify_email(
    verification: EmailVerification,
    db: Session = Depends(get_db)
):
    """
    Verify email address

    Args:
        verification: Email verification token
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If token is invalid or expired
    """
    email = verify_email_token(verification.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token"
        )

    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Mark user as verified
    user.is_verified = True
    db.commit()

    return {"message": "Email verified successfully"}


@router.post("/resend-verification")
async def resend_verification_email(
    current_user: User = Depends(get_current_active_user)
):
    """
    Resend email verification

    Args:
        current_user: Current authenticated user

    Returns:
        Success message

    Raises:
        HTTPException: If user is already verified
    """
    if current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already verified"
        )

    # Create verification token
    verification_token = create_email_verification_token(current_user.email)

    # TODO: Send verification email
    # For now, we'll just return the token (for development)
    # In production, this should send an email with the verification link

    print(f"Email verification token for {current_user.email}: {verification_token}")

    return {"message": "Verification email sent"}


@router.post("/logout")
async def logout_user(
    current_user: User = Depends(get_current_user)
):
    """
    Logout user (client-side token invalidation)

    Args:
        current_user: Current authenticated user

    Returns:
        Success message

    Note:
        Since we're using stateless JWT tokens, actual logout
        is handled on the client side by removing the token.
        This endpoint is provided for consistency.
    """
    return {"message": "Successfully logged out"}
